!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).WebStreamsPolyfill={})}(this,(function(e){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol:function(e){return"Symbol("+e+")"};function t(){}var o="undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:void 0;function n(e){return"object"==typeof e&&null!==e||"function"==typeof e}var a=t,i=Promise,l=Promise.prototype.then,u=Promise.resolve.bind(i),s=Promise.reject.bind(i);function c(e){return new i(e)}function d(e){return u(e)}function f(e){return s(e)}function b(e,r,t){return l.call(e,r,t)}function p(e,r,t){b(b(e,r,t),void 0,a)}function _(e,r){p(e,r)}function h(e,r){p(e,void 0,r)}function m(e,r,t){return b(e,r,t)}function y(e){b(e,void 0,a)}var v=function(){var e=o&&o.queueMicrotask;if("function"==typeof e)return e;var r=d(void 0);return function(e){return b(r,e)}}();function g(e,r,t){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,r,t)}function S(e,r,t){try{return d(g(e,r,t))}catch(e){return f(e)}}var w=function(){function e(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}return Object.defineProperty(e.prototype,"length",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.push=function(e){var r=this._back,t=r;16383===r._elements.length&&(t={_elements:[],_next:void 0}),r._elements.push(e),t!==r&&(this._back=t,r._next=t),++this._size},e.prototype.shift=function(){var e=this._front,r=e,t=this._cursor,o=t+1,n=e._elements,a=n[t];return 16384===o&&(r=e._next,o=0),--this._size,this._cursor=o,e!==r&&(this._front=r),n[t]=void 0,a},e.prototype.forEach=function(e){for(var r=this._cursor,t=this._front,o=t._elements;!(r===o.length&&void 0===t._next||r===o.length&&(r=0,0===(o=(t=t._next)._elements).length));)e(o[r]),++r},e.prototype.peek=function(){var e=this._front,r=this._cursor;return e._elements[r]},e}();function R(e,r){e._ownerReadableStream=r,r._reader=e,"readable"===r._state?q(e):"closed"===r._state?function(e){q(e),W(e)}(e):O(e,r._storedError)}function T(e,r){return Tt(e._ownerReadableStream,r)}function C(e){"readable"===e._ownerReadableStream._state?E(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,r){O(e,r)}(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function P(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function q(e){e._closedPromise=c((function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t}))}function O(e,r){q(e),E(e,r)}function E(e,r){void 0!==e._closedPromise_reject&&(y(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function W(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}var j=r("[[AbortSteps]]"),B=r("[[ErrorSteps]]"),k=r("[[CancelSteps]]"),A=r("[[PullSteps]]"),z=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},D=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function I(e,r){if(void 0!==e&&("object"!=typeof(t=e)&&"function"!=typeof t))throw new TypeError(r+" is not an object.");var t}function F(e,r){if("function"!=typeof e)throw new TypeError(r+" is not a function.")}function L(e,r){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError(r+" is not an object.")}function M(e,r,t){if(void 0===e)throw new TypeError("Parameter "+r+" is required in '"+t+"'.")}function Q(e,r,t){if(void 0===e)throw new TypeError(r+" is required in '"+t+"'.")}function Y(e){return Number(e)}function x(e){return 0===e?0:e}function N(e,r){var t=Number.MAX_SAFE_INTEGER,o=Number(e);if(o=x(o),!z(o))throw new TypeError(r+" is not a finite number");if((o=function(e){return x(D(e))}(o))<0||o>t)throw new TypeError(r+" is outside the accepted range of 0 to "+t+", inclusive");return z(o)&&0!==o?o:0}function H(e,r){if(!wt(e))throw new TypeError(r+" is not a ReadableStream.")}function V(e){return new $(e)}function U(e,r){e._reader._readRequests.push(r)}function G(e,r,t){var o=e._reader._readRequests.shift();t?o._closeSteps():o._chunkSteps(r)}function X(e){return e._reader._readRequests.length}function J(e){var r=e._reader;return void 0!==r&&!!ee(r)}var K,Z,$=function(){function ReadableStreamDefaultReader(e){if(M(e,1,"ReadableStreamDefaultReader"),H(e,"First parameter"),Rt(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");R(this,e),this._readRequests=new w}return Object.defineProperty(ReadableStreamDefaultReader.prototype,"closed",{get:function(){return ee(this)?this._closedPromise:f(te("closed"))},enumerable:!1,configurable:!0}),ReadableStreamDefaultReader.prototype.cancel=function(e){return void 0===e&&(e=void 0),ee(this)?void 0===this._ownerReadableStream?f(P("cancel")):T(this,e):f(te("cancel"))},ReadableStreamDefaultReader.prototype.read=function(){if(!ee(this))return f(te("read"));if(void 0===this._ownerReadableStream)return f(P("read from"));var e,r,t=c((function(t,o){e=t,r=o}));return re(this,{_chunkSteps:function(r){return e({value:r,done:!1})},_closeSteps:function(){return e({value:void 0,done:!0})},_errorSteps:function(e){return r(e)}}),t},ReadableStreamDefaultReader.prototype.releaseLock=function(){if(!ee(this))throw te("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");C(this)}},ReadableStreamDefaultReader}();function ee(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof $)}function re(e,r){var t=e._ownerReadableStream;t._disturbed=!0,"closed"===t._state?r._closeSteps():"errored"===t._state?r._errorSteps(t._storedError):t._readableStreamController[A](r)}function te(e){return new TypeError("ReadableStreamDefaultReader.prototype."+e+" can only be used on a ReadableStreamDefaultReader")}Object.defineProperties($.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty($.prototype,r.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0}),"symbol"==typeof r.asyncIterator&&((K={})[r.asyncIterator]=function(){return this},Z=K,Object.defineProperty(Z,r.asyncIterator,{enumerable:!1}));var oe=function(){function e(e,r){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=r}return e.prototype.next=function(){var e=this,r=function(){return e._nextSteps()};return this._ongoingPromise=this._ongoingPromise?m(this._ongoingPromise,r,r):r(),this._ongoingPromise},e.prototype.return=function(e){var r=this,t=function(){return r._returnSteps(e)};return this._ongoingPromise?m(this._ongoingPromise,t,t):t()},e.prototype._nextSteps=function(){var e=this;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});var r,t,o=this._reader;if(void 0===o._ownerReadableStream)return f(P("iterate"));var n=c((function(e,o){r=e,t=o}));return re(o,{_chunkSteps:function(t){e._ongoingPromise=void 0,v((function(){return r({value:t,done:!1})}))},_closeSteps:function(){e._ongoingPromise=void 0,e._isFinished=!0,C(o),r({value:void 0,done:!0})},_errorSteps:function(r){e._ongoingPromise=void 0,e._isFinished=!0,C(o),t(r)}}),n},e.prototype._returnSteps=function(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;var r=this._reader;if(void 0===r._ownerReadableStream)return f(P("finish iterating"));if(!this._preventCancel){var t=T(r,e);return C(r),m(t,(function(){return{value:e,done:!0}}))}return C(r),d({value:e,done:!0})},e}(),ne={next:function(){return ae(this)?this._asyncIteratorImpl.next():f(ie("next"))},return:function(e){return ae(this)?this._asyncIteratorImpl.return(e):f(ie("return"))}};function ae(e){if(!n(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof oe}catch(e){return!1}}function ie(e){return new TypeError("ReadableStreamAsyncIterator."+e+" can only be used on a ReadableSteamAsyncIterator")}void 0!==Z&&Object.setPrototypeOf(ne,Z);var le=Number.isNaN||function(e){return e!=e};function ue(e){return e.slice()}function se(e,r,t,o,n){new Uint8Array(e).set(new Uint8Array(t,o,n),r)}function ce(e,r,t){if(e.slice)return e.slice(r,t);var o=t-r,n=new ArrayBuffer(o);return se(n,0,e,r,o),n}function de(e){var r=ce(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(r)}function fe(e){var r=e._queue.shift();return e._queueTotalSize-=r.size,e._queueTotalSize<0&&(e._queueTotalSize=0),r.value}function be(e,r,t){if("number"!=typeof(o=t)||le(o)||o<0||t===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var o;e._queue.push({value:r,size:t}),e._queueTotalSize+=t}function pe(e){e._queue=new w,e._queueTotalSize=0}var _e=function(){function ReadableStreamBYOBRequest(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableStreamBYOBRequest.prototype,"view",{get:function(){if(!ye(this))throw Me("view");return this._view},enumerable:!1,configurable:!0}),ReadableStreamBYOBRequest.prototype.respond=function(e){if(!ye(this))throw Me("respond");if(M(e,1,"respond"),e=N(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");this._view.buffer,Ie(this._associatedReadableByteStreamController,e)},ReadableStreamBYOBRequest.prototype.respondWithNewView=function(e){if(!ye(this))throw Me("respondWithNewView");if(M(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");e.buffer,Fe(this._associatedReadableByteStreamController,e)},ReadableStreamBYOBRequest}();Object.defineProperties(_e.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(_e.prototype,r.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});var he=function(){function ReadableByteStreamController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableByteStreamController.prototype,"byobRequest",{get:function(){if(!me(this))throw Qe("byobRequest");return ze(this)},enumerable:!1,configurable:!0}),Object.defineProperty(ReadableByteStreamController.prototype,"desiredSize",{get:function(){if(!me(this))throw Qe("desiredSize");return De(this)},enumerable:!1,configurable:!0}),ReadableByteStreamController.prototype.close=function(){if(!me(this))throw Qe("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError("The stream (in "+e+" state) is not in the readable state and cannot be closed");Be(this)},ReadableByteStreamController.prototype.enqueue=function(e){if(!me(this))throw Qe("enqueue");if(M(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");var r=this._controlledReadableByteStream._state;if("readable"!==r)throw new TypeError("The stream (in "+r+" state) is not in the readable state and cannot be enqueued to");ke(this,e)},ReadableByteStreamController.prototype.error=function(e){if(void 0===e&&(e=void 0),!me(this))throw Qe("error");Ae(this,e)},ReadableByteStreamController.prototype[k]=function(e){ge(this),pe(this);var r=this._cancelAlgorithm(e);return je(this),r},ReadableByteStreamController.prototype[A]=function(e){var r=this._controlledReadableByteStream;if(this._queueTotalSize>0){var t=this._queue.shift();this._queueTotalSize-=t.byteLength,Pe(this);var o=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);e._chunkSteps(o)}else{var n=this._autoAllocateChunkSize;if(void 0!==n){var a=void 0;try{a=new ArrayBuffer(n)}catch(r){return void e._errorSteps(r)}var i={buffer:a,bufferByteLength:n,byteOffset:0,byteLength:n,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(i)}U(r,e),ve(this)}},ReadableByteStreamController}();function me(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof he)}function ye(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof _e)}function ve(e){(function(e){var r=e._controlledReadableByteStream;if("readable"!==r._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(J(r)&&X(r)>0)return!0;if(He(r)&&Ne(r)>0)return!0;if(De(e)>0)return!0;return!1})(e)&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,p(e._pullAlgorithm(),(function(){e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,ve(e))}),(function(r){Ae(e,r)}))))}function ge(e){qe(e),e._pendingPullIntos=new w}function Se(e,r){var t=!1;"closed"===e._state&&(t=!0);var o=we(r);"default"===r.readerType?G(e,o,t):function(e,r,t){var o=e._reader._readIntoRequests.shift();t?o._closeSteps(r):o._chunkSteps(r)}(e,o,t)}function we(e){var r=e.bytesFilled,t=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,r/t)}function Re(e,r,t,o){e._queue.push({buffer:r,byteOffset:t,byteLength:o}),e._queueTotalSize+=o}function Te(e,r){var t=r.elementSize,o=r.bytesFilled-r.bytesFilled%t,n=Math.min(e._queueTotalSize,r.byteLength-r.bytesFilled),a=r.bytesFilled+n,i=a-a%t,l=n,u=!1;i>o&&(l=i-r.bytesFilled,u=!0);for(var s=e._queue;l>0;){var c=s.peek(),d=Math.min(l,c.byteLength),f=r.byteOffset+r.bytesFilled;se(r.buffer,f,c.buffer,c.byteOffset,d),c.byteLength===d?s.shift():(c.byteOffset+=d,c.byteLength-=d),e._queueTotalSize-=d,Ce(e,d,r),l-=d}return u}function Ce(e,r,t){t.bytesFilled+=r}function Pe(e){0===e._queueTotalSize&&e._closeRequested?(je(e),Ct(e._controlledReadableByteStream)):ve(e)}function qe(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function Oe(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;var r=e._pendingPullIntos.peek();Te(e,r)&&(We(e),Se(e._controlledReadableByteStream,r))}}function Ee(e,r){var t=e._pendingPullIntos.peek();qe(e),"closed"===e._controlledReadableByteStream._state?function(e,r){var t=e._controlledReadableByteStream;if(He(t))for(;Ne(t)>0;)Se(t,We(e))}(e):function(e,r,t){if(Ce(0,r,t),!(t.bytesFilled<t.elementSize)){We(e);var o=t.bytesFilled%t.elementSize;if(o>0){var n=t.byteOffset+t.bytesFilled,a=ce(t.buffer,n-o,n);Re(e,a,0,a.byteLength)}t.bytesFilled-=o,Se(e._controlledReadableByteStream,t),Oe(e)}}(e,r,t),ve(e)}function We(e){return e._pendingPullIntos.shift()}function je(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function Be(e){var r=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===r._state)if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0)if(e._pendingPullIntos.peek().bytesFilled>0){var t=new TypeError("Insufficient bytes to fill elements in the given buffer");throw Ae(e,t),t}je(e),Ct(r)}}function ke(e,r){var t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state){var o=r.buffer,n=r.byteOffset,a=r.byteLength,i=o;if(e._pendingPullIntos.length>0){var l=e._pendingPullIntos.peek();l.buffer,0,l.buffer=l.buffer}if(qe(e),J(t))if(0===X(t))Re(e,i,n,a);else e._pendingPullIntos.length>0&&We(e),G(t,new Uint8Array(i,n,a),!1);else He(t)?(Re(e,i,n,a),Oe(e)):Re(e,i,n,a);ve(e)}}function Ae(e,r){var t=e._controlledReadableByteStream;"readable"===t._state&&(ge(e),pe(e),je(e),Pt(t,r))}function ze(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){var r=e._pendingPullIntos.peek(),t=new Uint8Array(r.buffer,r.byteOffset+r.bytesFilled,r.byteLength-r.bytesFilled),o=Object.create(_e.prototype);!function(e,r,t){e._associatedReadableByteStreamController=r,e._view=t}(o,e,t),e._byobRequest=o}return e._byobRequest}function De(e){var r=e._controlledReadableByteStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function Ie(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===r)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(t.bytesFilled+r>t.byteLength)throw new RangeError("bytesWritten out of range")}t.buffer=t.buffer,Ee(e,r)}function Fe(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===r.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(t.byteOffset+t.bytesFilled!==r.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(t.bufferByteLength!==r.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(t.bytesFilled+r.byteLength>t.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");var o=r.byteLength;t.buffer=r.buffer,Ee(e,o)}function Le(e,r,t,o,n,a,i){r._controlledReadableByteStream=e,r._pullAgain=!1,r._pulling=!1,r._byobRequest=null,r._queue=r._queueTotalSize=void 0,pe(r),r._closeRequested=!1,r._started=!1,r._strategyHWM=a,r._pullAlgorithm=o,r._cancelAlgorithm=n,r._autoAllocateChunkSize=i,r._pendingPullIntos=new w,e._readableStreamController=r,p(d(t()),(function(){r._started=!0,ve(r)}),(function(e){Ae(r,e)}))}function Me(e){return new TypeError("ReadableStreamBYOBRequest.prototype."+e+" can only be used on a ReadableStreamBYOBRequest")}function Qe(e){return new TypeError("ReadableByteStreamController.prototype."+e+" can only be used on a ReadableByteStreamController")}function Ye(e){return new Ve(e)}function xe(e,r){e._reader._readIntoRequests.push(r)}function Ne(e){return e._reader._readIntoRequests.length}function He(e){var r=e._reader;return void 0!==r&&!!Ue(r)}Object.defineProperties(he.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(he.prototype,r.toStringTag,{value:"ReadableByteStreamController",configurable:!0});var Ve=function(){function ReadableStreamBYOBReader(e){if(M(e,1,"ReadableStreamBYOBReader"),H(e,"First parameter"),Rt(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!me(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");R(this,e),this._readIntoRequests=new w}return Object.defineProperty(ReadableStreamBYOBReader.prototype,"closed",{get:function(){return Ue(this)?this._closedPromise:f(Xe("closed"))},enumerable:!1,configurable:!0}),ReadableStreamBYOBReader.prototype.cancel=function(e){return void 0===e&&(e=void 0),Ue(this)?void 0===this._ownerReadableStream?f(P("cancel")):T(this,e):f(Xe("cancel"))},ReadableStreamBYOBReader.prototype.read=function(e){if(!Ue(this))return f(Xe("read"));if(!ArrayBuffer.isView(e))return f(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return f(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return f(new TypeError("view's buffer must have non-zero byteLength"));if(e.buffer,void 0===this._ownerReadableStream)return f(P("read from"));var r,t,o=c((function(e,o){r=e,t=o}));return Ge(this,e,{_chunkSteps:function(e){return r({value:e,done:!1})},_closeSteps:function(e){return r({value:e,done:!0})},_errorSteps:function(e){return t(e)}}),o},ReadableStreamBYOBReader.prototype.releaseLock=function(){if(!Ue(this))throw Xe("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");C(this)}},ReadableStreamBYOBReader}();function Ue(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof Ve)}function Ge(e,r,t){var o=e._ownerReadableStream;o._disturbed=!0,"errored"===o._state?t._errorSteps(o._storedError):function(e,r,t){var o=e._controlledReadableByteStream,n=1;r.constructor!==DataView&&(n=r.constructor.BYTES_PER_ELEMENT);var a=r.constructor,i=r.buffer,l={buffer:i,bufferByteLength:i.byteLength,byteOffset:r.byteOffset,byteLength:r.byteLength,bytesFilled:0,elementSize:n,viewConstructor:a,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(l),void xe(o,t);if("closed"!==o._state){if(e._queueTotalSize>0){if(Te(e,l)){var u=we(l);return Pe(e),void t._chunkSteps(u)}if(e._closeRequested){var s=new TypeError("Insufficient bytes to fill elements in the given buffer");return Ae(e,s),void t._errorSteps(s)}}e._pendingPullIntos.push(l),xe(o,t),ve(e)}else{var c=new a(l.buffer,l.byteOffset,0);t._closeSteps(c)}}(o._readableStreamController,r,t)}function Xe(e){return new TypeError("ReadableStreamBYOBReader.prototype."+e+" can only be used on a ReadableStreamBYOBReader")}function Je(e,r){var t=e.highWaterMark;if(void 0===t)return r;if(le(t)||t<0)throw new RangeError("Invalid highWaterMark");return t}function Ke(e){var r=e.size;return r||function(){return 1}}function Ze(e,r){I(e,r);var t=null==e?void 0:e.highWaterMark,o=null==e?void 0:e.size;return{highWaterMark:void 0===t?void 0:Y(t),size:void 0===o?void 0:$e(o,r+" has member 'size' that")}}function $e(e,r){return F(e,r),function(r){return Y(e(r))}}function er(e,r,t){return F(e,t),function(t){return S(e,r,[t])}}function rr(e,r,t){return F(e,t),function(){return S(e,r,[])}}function tr(e,r,t){return F(e,t),function(t){return g(e,r,[t])}}function or(e,r,t){return F(e,t),function(t,o){return S(e,r,[t,o])}}function nr(e,r){if(!sr(e))throw new TypeError(r+" is not a WritableStream.")}Object.defineProperties(Ve.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(Ve.prototype,r.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});var ar="function"==typeof AbortController;var ir=function(){function WritableStream(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:L(e,"First parameter");var t=Ze(r,"Second parameter"),o=function(e,r){I(e,r);var t=null==e?void 0:e.abort,o=null==e?void 0:e.close,n=null==e?void 0:e.start,a=null==e?void 0:e.type,i=null==e?void 0:e.write;return{abort:void 0===t?void 0:er(t,e,r+" has member 'abort' that"),close:void 0===o?void 0:rr(o,e,r+" has member 'close' that"),start:void 0===n?void 0:tr(n,e,r+" has member 'start' that"),write:void 0===i?void 0:or(i,e,r+" has member 'write' that"),type:a}}(e,"First parameter");if(ur(this),void 0!==o.type)throw new RangeError("Invalid type is specified");var n=Ke(t);!function(e,r,t,o){var n=Object.create(qr.prototype),a=function(){},i=function(){return d(void 0)},l=function(){return d(void 0)},u=function(){return d(void 0)};void 0!==r.start&&(a=function(){return r.start(n)});void 0!==r.write&&(i=function(e){return r.write(e,n)});void 0!==r.close&&(l=function(){return r.close()});void 0!==r.abort&&(u=function(e){return r.abort(e)});Er(e,n,a,i,l,u,t,o)}(this,o,Je(t,1),n)}return Object.defineProperty(WritableStream.prototype,"locked",{get:function(){if(!sr(this))throw Dr("locked");return cr(this)},enumerable:!1,configurable:!0}),WritableStream.prototype.abort=function(e){return void 0===e&&(e=void 0),sr(this)?cr(this)?f(new TypeError("Cannot abort a stream that already has a writer")):dr(this,e):f(Dr("abort"))},WritableStream.prototype.close=function(){return sr(this)?cr(this)?f(new TypeError("Cannot close a stream that already has a writer")):hr(this)?f(new TypeError("Cannot close an already-closing stream")):fr(this):f(Dr("close"))},WritableStream.prototype.getWriter=function(){if(!sr(this))throw Dr("getWriter");return lr(this)},WritableStream}();function lr(e){return new vr(e)}function ur(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new w,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function sr(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof ir)}function cr(e){return void 0!==e._writer}function dr(e,r){var t;if("closed"===e._state||"errored"===e._state)return d(void 0);e._writableStreamController._abortReason=r,null===(t=e._writableStreamController._abortController)||void 0===t||t.abort();var o=e._state;if("closed"===o||"errored"===o)return d(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;var n=!1;"erroring"===o&&(n=!0,r=void 0);var a=c((function(t,o){e._pendingAbortRequest={_promise:void 0,_resolve:t,_reject:o,_reason:r,_wasAlreadyErroring:n}}));return e._pendingAbortRequest._promise=a,n||pr(e,r),a}function fr(e){var r=e._state;if("closed"===r||"errored"===r)return f(new TypeError("The stream (in "+r+" state) is not in the writable state and cannot be closed"));var t,o=c((function(r,t){var o={_resolve:r,_reject:t};e._closeRequest=o})),n=e._writer;return void 0!==n&&e._backpressure&&"writable"===r&&Gr(n),be(t=e._writableStreamController,Pr,0),Br(t),o}function br(e,r){"writable"!==e._state?_r(e):pr(e,r)}function pr(e,r){var t=e._writableStreamController;e._state="erroring",e._storedError=r;var o=e._writer;void 0!==o&&Rr(o,r),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&t._started&&_r(e)}function _r(e){e._state="errored",e._writableStreamController[B]();var r=e._storedError;if(e._writeRequests.forEach((function(e){e._reject(r)})),e._writeRequests=new w,void 0!==e._pendingAbortRequest){var t=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,t._wasAlreadyErroring)return t._reject(r),void mr(e);p(e._writableStreamController[j](t._reason),(function(){t._resolve(),mr(e)}),(function(r){t._reject(r),mr(e)}))}else mr(e)}function hr(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function mr(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);var r=e._writer;void 0!==r&&Yr(r,e._storedError)}function yr(e,r){var t=e._writer;void 0!==t&&r!==e._backpressure&&(r?function(e){Nr(e)}(t):Gr(t)),e._backpressure=r}Object.defineProperties(ir.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(ir.prototype,r.toStringTag,{value:"WritableStream",configurable:!0});var vr=function(){function WritableStreamDefaultWriter(e){if(M(e,1,"WritableStreamDefaultWriter"),nr(e,"First parameter"),cr(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;var r,t=e._state;if("writable"===t)!hr(e)&&e._backpressure?Nr(this):Vr(this),Mr(this);else if("erroring"===t)Hr(this,e._storedError),Mr(this);else if("closed"===t)Vr(this),Mr(r=this),xr(r);else{var o=e._storedError;Hr(this,o),Qr(this,o)}}return Object.defineProperty(WritableStreamDefaultWriter.prototype,"closed",{get:function(){return gr(this)?this._closedPromise:f(Fr("closed"))},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultWriter.prototype,"desiredSize",{get:function(){if(!gr(this))throw Fr("desiredSize");if(void 0===this._ownerWritableStream)throw Lr("desiredSize");return function(e){var r=e._ownerWritableStream,t=r._state;if("errored"===t||"erroring"===t)return null;if("closed"===t)return 0;return jr(r._writableStreamController)}(this)},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultWriter.prototype,"ready",{get:function(){return gr(this)?this._readyPromise:f(Fr("ready"))},enumerable:!1,configurable:!0}),WritableStreamDefaultWriter.prototype.abort=function(e){return void 0===e&&(e=void 0),gr(this)?void 0===this._ownerWritableStream?f(Lr("abort")):function(e,r){return dr(e._ownerWritableStream,r)}(this,e):f(Fr("abort"))},WritableStreamDefaultWriter.prototype.close=function(){if(!gr(this))return f(Fr("close"));var e=this._ownerWritableStream;return void 0===e?f(Lr("close")):hr(e)?f(new TypeError("Cannot close an already-closing stream")):Sr(this)},WritableStreamDefaultWriter.prototype.releaseLock=function(){if(!gr(this))throw Fr("releaseLock");void 0!==this._ownerWritableStream&&Tr(this)},WritableStreamDefaultWriter.prototype.write=function(e){return void 0===e&&(e=void 0),gr(this)?void 0===this._ownerWritableStream?f(Lr("write to")):Cr(this,e):f(Fr("write"))},WritableStreamDefaultWriter}();function gr(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof vr)}function Sr(e){return fr(e._ownerWritableStream)}function wr(e,r){"pending"===e._closedPromiseState?Yr(e,r):function(e,r){Qr(e,r)}(e,r)}function Rr(e,r){"pending"===e._readyPromiseState?Ur(e,r):function(e,r){Hr(e,r)}(e,r)}function Tr(e){var r=e._ownerWritableStream,t=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");Rr(e,t),wr(e,t),r._writer=void 0,e._ownerWritableStream=void 0}function Cr(e,r){var t=e._ownerWritableStream,o=t._writableStreamController,n=function(e,r){try{return e._strategySizeAlgorithm(r)}catch(r){return kr(e,r),1}}(o,r);if(t!==e._ownerWritableStream)return f(Lr("write to"));var a=t._state;if("errored"===a)return f(t._storedError);if(hr(t)||"closed"===a)return f(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return f(t._storedError);var i=function(e){return c((function(r,t){var o={_resolve:r,_reject:t};e._writeRequests.push(o)}))}(t);return function(e,r,t){try{be(e,r,t)}catch(r){return void kr(e,r)}var o=e._controlledWritableStream;if(!hr(o)&&"writable"===o._state){yr(o,Ar(e))}Br(e)}(o,r,n),i}Object.defineProperties(vr.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(vr.prototype,r.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});var Pr={},qr=function(){function WritableStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(WritableStreamDefaultController.prototype,"abortReason",{get:function(){if(!Or(this))throw Ir("abortReason");return this._abortReason},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultController.prototype,"signal",{get:function(){if(!Or(this))throw Ir("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal},enumerable:!1,configurable:!0}),WritableStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!Or(this))throw Ir("error");"writable"===this._controlledWritableStream._state&&zr(this,e)},WritableStreamDefaultController.prototype[j]=function(e){var r=this._abortAlgorithm(e);return Wr(this),r},WritableStreamDefaultController.prototype[B]=function(){pe(this)},WritableStreamDefaultController}();function Or(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof qr)}function Er(e,r,t,o,n,a,i,l){r._controlledWritableStream=e,e._writableStreamController=r,r._queue=void 0,r._queueTotalSize=void 0,pe(r),r._abortReason=void 0,r._abortController=function(){if(ar)return new AbortController}(),r._started=!1,r._strategySizeAlgorithm=l,r._strategyHWM=i,r._writeAlgorithm=o,r._closeAlgorithm=n,r._abortAlgorithm=a;var u=Ar(r);yr(e,u),p(d(t()),(function(){r._started=!0,Br(r)}),(function(t){r._started=!0,br(e,t)}))}function Wr(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function jr(e){return e._strategyHWM-e._queueTotalSize}function Br(e){var r=e._controlledWritableStream;if(e._started&&void 0===r._inFlightWriteRequest)if("erroring"!==r._state){if(0!==e._queue.length){var t=e._queue.peek().value;t===Pr?function(e){var r=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(r),fe(e);var t=e._closeAlgorithm();Wr(e),p(t,(function(){!function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";var r=e._writer;void 0!==r&&xr(r)}(r)}),(function(e){!function(e,r){e._inFlightCloseRequest._reject(r),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(r),e._pendingAbortRequest=void 0),br(e,r)}(r,e)}))}(e):function(e,r){var t=e._controlledWritableStream;(function(e){e._inFlightWriteRequest=e._writeRequests.shift()})(t),p(e._writeAlgorithm(r),(function(){!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(t);var r=t._state;if(fe(e),!hr(t)&&"writable"===r){var o=Ar(e);yr(t,o)}Br(e)}),(function(r){"writable"===t._state&&Wr(e),function(e,r){e._inFlightWriteRequest._reject(r),e._inFlightWriteRequest=void 0,br(e,r)}(t,r)}))}(e,t)}}else _r(r)}function kr(e,r){"writable"===e._controlledWritableStream._state&&zr(e,r)}function Ar(e){return jr(e)<=0}function zr(e,r){var t=e._controlledWritableStream;Wr(e),pr(t,r)}function Dr(e){return new TypeError("WritableStream.prototype."+e+" can only be used on a WritableStream")}function Ir(e){return new TypeError("WritableStreamDefaultController.prototype."+e+" can only be used on a WritableStreamDefaultController")}function Fr(e){return new TypeError("WritableStreamDefaultWriter.prototype."+e+" can only be used on a WritableStreamDefaultWriter")}function Lr(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function Mr(e){e._closedPromise=c((function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t,e._closedPromiseState="pending"}))}function Qr(e,r){Mr(e),Yr(e,r)}function Yr(e,r){void 0!==e._closedPromise_reject&&(y(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function xr(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function Nr(e){e._readyPromise=c((function(r,t){e._readyPromise_resolve=r,e._readyPromise_reject=t})),e._readyPromiseState="pending"}function Hr(e,r){Nr(e),Ur(e,r)}function Vr(e){Nr(e),Gr(e)}function Ur(e,r){void 0!==e._readyPromise_reject&&(y(e._readyPromise),e._readyPromise_reject(r),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function Gr(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(qr.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(qr.prototype,r.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});var Xr="undefined"!=typeof DOMException?DOMException:void 0;var Jr,Kr=function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;try{return new e,!0}catch(e){return!1}}(Xr)?Xr:((Jr=function(e,r){this.message=e||"",this.name=r||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}).prototype=Object.create(Error.prototype),Object.defineProperty(Jr.prototype,"constructor",{value:Jr,writable:!0,configurable:!0}),Jr);function Zr(e,r,o,n,a,i){var l=V(e),u=lr(r);e._disturbed=!0;var s=!1,m=d(void 0);return c((function(v,g){var S,w,R,T;if(void 0!==i){if(S=function(){var t=new Kr("Aborted","AbortError"),o=[];n||o.push((function(){return"writable"===r._state?dr(r,t):d(void 0)})),a||o.push((function(){return"readable"===e._state?Tt(e,t):d(void 0)})),E((function(){return Promise.all(o.map((function(e){return e()})))}),!0,t)},i.aborted)return void S();i.addEventListener("abort",S)}if(O(e,l._closedPromise,(function(e){n?W(!0,e):E((function(){return dr(r,e)}),!0,e)})),O(r,u._closedPromise,(function(r){a?W(!0,r):E((function(){return Tt(e,r)}),!0,r)})),w=e,R=l._closedPromise,T=function(){o?W():E((function(){return function(e){var r=e._ownerWritableStream,t=r._state;return hr(r)||"closed"===t?d(void 0):"errored"===t?f(r._storedError):Sr(e)}(u)}))},"closed"===w._state?T():_(R,T),hr(r)||"closed"===r._state){var P=new TypeError("the destination writable stream closed before all data could be piped to it");a?W(!0,P):E((function(){return Tt(e,P)}),!0,P)}function q(){var e=m;return b(m,(function(){return e!==m?q():void 0}))}function O(e,r,t){"errored"===e._state?t(e._storedError):h(r,t)}function E(e,t,o){function n(){p(e(),(function(){return j(t,o)}),(function(e){return j(!0,e)}))}s||(s=!0,"writable"!==r._state||hr(r)?n():_(q(),n))}function W(e,t){s||(s=!0,"writable"!==r._state||hr(r)?j(e,t):_(q(),(function(){return j(e,t)})))}function j(e,r){Tr(u),C(l),void 0!==i&&i.removeEventListener("abort",S),e?g(r):v(void 0)}y(c((function(e,r){!function o(n){n?e():b(s?d(!0):b(u._readyPromise,(function(){return c((function(e,r){re(l,{_chunkSteps:function(r){m=b(Cr(u,r),void 0,t),e(!1)},_closeSteps:function(){return e(!0)},_errorSteps:r})}))})),o,r)}(!1)})))}))}var $r=function(){function ReadableStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableStreamDefaultController.prototype,"desiredSize",{get:function(){if(!et(this))throw ct("desiredSize");return lt(this)},enumerable:!1,configurable:!0}),ReadableStreamDefaultController.prototype.close=function(){if(!et(this))throw ct("close");if(!ut(this))throw new TypeError("The stream is not in a state that permits close");nt(this)},ReadableStreamDefaultController.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!et(this))throw ct("enqueue");if(!ut(this))throw new TypeError("The stream is not in a state that permits enqueue");return at(this,e)},ReadableStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!et(this))throw ct("error");it(this,e)},ReadableStreamDefaultController.prototype[k]=function(e){pe(this);var r=this._cancelAlgorithm(e);return ot(this),r},ReadableStreamDefaultController.prototype[A]=function(e){var r=this._controlledReadableStream;if(this._queue.length>0){var t=fe(this);this._closeRequested&&0===this._queue.length?(ot(this),Ct(r)):rt(this),e._chunkSteps(t)}else U(r,e),rt(this)},ReadableStreamDefaultController}();function et(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof $r)}function rt(e){tt(e)&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,p(e._pullAlgorithm(),(function(){e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,rt(e))}),(function(r){it(e,r)}))))}function tt(e){var r=e._controlledReadableStream;return!!ut(e)&&(!!e._started&&(!!(Rt(r)&&X(r)>0)||lt(e)>0))}function ot(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function nt(e){if(ut(e)){var r=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(ot(e),Ct(r))}}function at(e,r){if(ut(e)){var t=e._controlledReadableStream;if(Rt(t)&&X(t)>0)G(t,r,!1);else{var o=void 0;try{o=e._strategySizeAlgorithm(r)}catch(r){throw it(e,r),r}try{be(e,r,o)}catch(r){throw it(e,r),r}}rt(e)}}function it(e,r){var t=e._controlledReadableStream;"readable"===t._state&&(pe(e),ot(e),Pt(t,r))}function lt(e){var r=e._controlledReadableStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function ut(e){var r=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===r}function st(e,r,t,o,n,a,i){r._controlledReadableStream=e,r._queue=void 0,r._queueTotalSize=void 0,pe(r),r._started=!1,r._closeRequested=!1,r._pullAgain=!1,r._pulling=!1,r._strategySizeAlgorithm=i,r._strategyHWM=a,r._pullAlgorithm=o,r._cancelAlgorithm=n,e._readableStreamController=r,p(d(t()),(function(){r._started=!0,rt(r)}),(function(e){it(r,e)}))}function ct(e){return new TypeError("ReadableStreamDefaultController.prototype."+e+" can only be used on a ReadableStreamDefaultController")}function dt(e,r){return me(e._readableStreamController)?function(e){var r,t,o,n,a,i=V(e),l=!1,u=!1,s=!1,f=!1,b=!1,p=c((function(e){a=e}));function _(e){h(e._closedPromise,(function(r){e===i&&(Ae(o._readableStreamController,r),Ae(n._readableStreamController,r),f&&b||a(void 0))}))}function m(){Ue(i)&&(C(i),_(i=V(e))),re(i,{_chunkSteps:function(r){v((function(){u=!1,s=!1;var t=r,i=r;if(!f&&!b)try{i=de(r)}catch(r){return Ae(o._readableStreamController,r),Ae(n._readableStreamController,r),void a(Tt(e,r))}f||ke(o._readableStreamController,t),b||ke(n._readableStreamController,i),l=!1,u?g():s&&S()}))},_closeSteps:function(){l=!1,f||Be(o._readableStreamController),b||Be(n._readableStreamController),o._readableStreamController._pendingPullIntos.length>0&&Ie(o._readableStreamController,0),n._readableStreamController._pendingPullIntos.length>0&&Ie(n._readableStreamController,0),f&&b||a(void 0)},_errorSteps:function(){l=!1}})}function y(r,t){ee(i)&&(C(i),_(i=Ye(e)));var c=t?n:o,d=t?o:n;Ge(i,r,{_chunkSteps:function(r){v((function(){u=!1,s=!1;var o=t?b:f;if(t?f:b)o||Fe(c._readableStreamController,r);else{var n=void 0;try{n=de(r)}catch(r){return Ae(c._readableStreamController,r),Ae(d._readableStreamController,r),void a(Tt(e,r))}o||Fe(c._readableStreamController,r),ke(d._readableStreamController,n)}l=!1,u?g():s&&S()}))},_closeSteps:function(e){l=!1;var r=t?b:f,o=t?f:b;r||Be(c._readableStreamController),o||Be(d._readableStreamController),void 0!==e&&(r||Fe(c._readableStreamController,e),!o&&d._readableStreamController._pendingPullIntos.length>0&&Ie(d._readableStreamController,0)),r&&o||a(void 0)},_errorSteps:function(){l=!1}})}function g(){if(l)return u=!0,d(void 0);l=!0;var e=ze(o._readableStreamController);return null===e?m():y(e._view,!1),d(void 0)}function S(){if(l)return s=!0,d(void 0);l=!0;var e=ze(n._readableStreamController);return null===e?m():y(e._view,!0),d(void 0)}function w(o){if(f=!0,r=o,b){var n=ue([r,t]),i=Tt(e,n);a(i)}return p}function R(o){if(b=!0,t=o,f){var n=ue([r,t]),i=Tt(e,n);a(i)}return p}function T(){}return o=gt(T,g,w),n=gt(T,S,R),_(i),[o,n]}(e):function(e,r){var t,o,n,a,i,l=V(e),u=!1,s=!1,f=!1,b=!1,p=c((function(e){i=e}));function _(){return u?(s=!0,d(void 0)):(u=!0,re(l,{_chunkSteps:function(e){v((function(){s=!1;var r=e,t=e;f||at(n._readableStreamController,r),b||at(a._readableStreamController,t),u=!1,s&&_()}))},_closeSteps:function(){u=!1,f||nt(n._readableStreamController),b||nt(a._readableStreamController),f&&b||i(void 0)},_errorSteps:function(){u=!1}}),d(void 0))}function m(r){if(f=!0,t=r,b){var n=ue([t,o]),a=Tt(e,n);i(a)}return p}function y(r){if(b=!0,o=r,f){var n=ue([t,o]),a=Tt(e,n);i(a)}return p}function g(){}return n=vt(g,_,m),a=vt(g,_,y),h(l._closedPromise,(function(e){it(n._readableStreamController,e),it(a._readableStreamController,e),f&&b||i(void 0)})),[n,a]}(e)}function ft(e,r,t){return F(e,t),function(t){return S(e,r,[t])}}function bt(e,r,t){return F(e,t),function(t){return S(e,r,[t])}}function pt(e,r,t){return F(e,t),function(t){return g(e,r,[t])}}function _t(e,r){if("bytes"!==(e=""+e))throw new TypeError(r+" '"+e+"' is not a valid enumeration value for ReadableStreamType");return e}function ht(e,r){if("byob"!==(e=""+e))throw new TypeError(r+" '"+e+"' is not a valid enumeration value for ReadableStreamReaderMode");return e}function mt(e,r){I(e,r);var t=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,a=null==e?void 0:e.signal;return void 0!==a&&function(e,r){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError(r+" is not an AbortSignal.")}(a,r+" has member 'signal' that"),{preventAbort:Boolean(t),preventCancel:Boolean(o),preventClose:Boolean(n),signal:a}}Object.defineProperties($r.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty($r.prototype,r.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});var yt=function(){function ReadableStream(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:L(e,"First parameter");var t=Ze(r,"Second parameter"),o=function(e,r){I(e,r);var t=e,o=null==t?void 0:t.autoAllocateChunkSize,n=null==t?void 0:t.cancel,a=null==t?void 0:t.pull,i=null==t?void 0:t.start,l=null==t?void 0:t.type;return{autoAllocateChunkSize:void 0===o?void 0:N(o,r+" has member 'autoAllocateChunkSize' that"),cancel:void 0===n?void 0:ft(n,t,r+" has member 'cancel' that"),pull:void 0===a?void 0:bt(a,t,r+" has member 'pull' that"),start:void 0===i?void 0:pt(i,t,r+" has member 'start' that"),type:void 0===l?void 0:_t(l,r+" has member 'type' that")}}(e,"First parameter");if(St(this),"bytes"===o.type){if(void 0!==t.size)throw new RangeError("The strategy for a byte stream cannot have a size function");!function(e,r,t){var o=Object.create(he.prototype),n=function(){},a=function(){return d(void 0)},i=function(){return d(void 0)};void 0!==r.start&&(n=function(){return r.start(o)}),void 0!==r.pull&&(a=function(){return r.pull(o)}),void 0!==r.cancel&&(i=function(e){return r.cancel(e)});var l=r.autoAllocateChunkSize;if(0===l)throw new TypeError("autoAllocateChunkSize must be greater than 0");Le(e,o,n,a,i,t,l)}(this,o,Je(t,0))}else{var n=Ke(t);!function(e,r,t,o){var n=Object.create($r.prototype),a=function(){},i=function(){return d(void 0)},l=function(){return d(void 0)};void 0!==r.start&&(a=function(){return r.start(n)}),void 0!==r.pull&&(i=function(){return r.pull(n)}),void 0!==r.cancel&&(l=function(e){return r.cancel(e)}),st(e,n,a,i,l,t,o)}(this,o,Je(t,1),n)}}return Object.defineProperty(ReadableStream.prototype,"locked",{get:function(){if(!wt(this))throw qt("locked");return Rt(this)},enumerable:!1,configurable:!0}),ReadableStream.prototype.cancel=function(e){return void 0===e&&(e=void 0),wt(this)?Rt(this)?f(new TypeError("Cannot cancel a stream that already has a reader")):Tt(this,e):f(qt("cancel"))},ReadableStream.prototype.getReader=function(e){if(void 0===e&&(e=void 0),!wt(this))throw qt("getReader");return void 0===function(e,r){I(e,r);var t=null==e?void 0:e.mode;return{mode:void 0===t?void 0:ht(t,r+" has member 'mode' that")}}(e,"First parameter").mode?V(this):Ye(this)},ReadableStream.prototype.pipeThrough=function(e,r){if(void 0===r&&(r={}),!wt(this))throw qt("pipeThrough");M(e,1,"pipeThrough");var t=function(e,r){I(e,r);var t=null==e?void 0:e.readable;Q(t,"readable","ReadableWritablePair"),H(t,r+" has member 'readable' that");var o=null==e?void 0:e.writable;return Q(o,"writable","ReadableWritablePair"),nr(o,r+" has member 'writable' that"),{readable:t,writable:o}}(e,"First parameter"),o=mt(r,"Second parameter");if(Rt(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(cr(t.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return y(Zr(this,t.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),t.readable},ReadableStream.prototype.pipeTo=function(e,r){if(void 0===r&&(r={}),!wt(this))return f(qt("pipeTo"));if(void 0===e)return f("Parameter 1 is required in 'pipeTo'.");if(!sr(e))return f(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));var t;try{t=mt(r,"Second parameter")}catch(e){return f(e)}return Rt(this)?f(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):cr(e)?f(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):Zr(this,e,t.preventClose,t.preventAbort,t.preventCancel,t.signal)},ReadableStream.prototype.tee=function(){if(!wt(this))throw qt("tee");return ue(dt(this))},ReadableStream.prototype.values=function(e){if(void 0===e&&(e=void 0),!wt(this))throw qt("values");var r,t,o,n,a,i=function(e,r){I(e,r);var t=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(t)}}(e,"First parameter");return r=this,t=i.preventCancel,o=V(r),n=new oe(o,t),(a=Object.create(ne))._asyncIteratorImpl=n,a},ReadableStream}();function vt(e,r,t,o,n){void 0===o&&(o=1),void 0===n&&(n=function(){return 1});var a=Object.create(yt.prototype);return St(a),st(a,Object.create($r.prototype),e,r,t,o,n),a}function gt(e,r,t){var o=Object.create(yt.prototype);return St(o),Le(o,Object.create(he.prototype),e,r,t,0,void 0),o}function St(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function wt(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof yt)}function Rt(e){return void 0!==e._reader}function Tt(e,r){if(e._disturbed=!0,"closed"===e._state)return d(void 0);if("errored"===e._state)return f(e._storedError);Ct(e);var o=e._reader;return void 0!==o&&Ue(o)&&(o._readIntoRequests.forEach((function(e){e._closeSteps(void 0)})),o._readIntoRequests=new w),m(e._readableStreamController[k](r),t)}function Ct(e){e._state="closed";var r=e._reader;void 0!==r&&(W(r),ee(r)&&(r._readRequests.forEach((function(e){e._closeSteps()})),r._readRequests=new w))}function Pt(e,r){e._state="errored",e._storedError=r;var t=e._reader;void 0!==t&&(E(t,r),ee(t)?(t._readRequests.forEach((function(e){e._errorSteps(r)})),t._readRequests=new w):(t._readIntoRequests.forEach((function(e){e._errorSteps(r)})),t._readIntoRequests=new w))}function qt(e){return new TypeError("ReadableStream.prototype."+e+" can only be used on a ReadableStream")}function Ot(e,r){I(e,r);var t=null==e?void 0:e.highWaterMark;return Q(t,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Y(t)}}Object.defineProperties(yt.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(yt.prototype,r.toStringTag,{value:"ReadableStream",configurable:!0}),"symbol"==typeof r.asyncIterator&&Object.defineProperty(yt.prototype,r.asyncIterator,{value:yt.prototype.values,writable:!0,configurable:!0});var Et=function(e){return e.byteLength};Object.defineProperty(Et,"name",{value:"size",configurable:!0});var Wt=function(){function ByteLengthQueuingStrategy(e){M(e,1,"ByteLengthQueuingStrategy"),e=Ot(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(ByteLengthQueuingStrategy.prototype,"highWaterMark",{get:function(){if(!Bt(this))throw jt("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(ByteLengthQueuingStrategy.prototype,"size",{get:function(){if(!Bt(this))throw jt("size");return Et},enumerable:!1,configurable:!0}),ByteLengthQueuingStrategy}();function jt(e){return new TypeError("ByteLengthQueuingStrategy.prototype."+e+" can only be used on a ByteLengthQueuingStrategy")}function Bt(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof Wt)}Object.defineProperties(Wt.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(Wt.prototype,r.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});var kt=function(){return 1};Object.defineProperty(kt,"name",{value:"size",configurable:!0});var At=function(){function CountQueuingStrategy(e){M(e,1,"CountQueuingStrategy"),e=Ot(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(CountQueuingStrategy.prototype,"highWaterMark",{get:function(){if(!Dt(this))throw zt("highWaterMark");return this._countQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(CountQueuingStrategy.prototype,"size",{get:function(){if(!Dt(this))throw zt("size");return kt},enumerable:!1,configurable:!0}),CountQueuingStrategy}();function zt(e){return new TypeError("CountQueuingStrategy.prototype."+e+" can only be used on a CountQueuingStrategy")}function Dt(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof At)}function It(e,r,t){return F(e,t),function(t){return S(e,r,[t])}}function Ft(e,r,t){return F(e,t),function(t){return g(e,r,[t])}}function Lt(e,r,t){return F(e,t),function(t,o){return S(e,r,[t,o])}}Object.defineProperties(At.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(At.prototype,r.toStringTag,{value:"CountQueuingStrategy",configurable:!0});var Mt=function(){function TransformStream(e,r,t){void 0===e&&(e={}),void 0===r&&(r={}),void 0===t&&(t={}),void 0===e&&(e=null);var o=Ze(r,"Second parameter"),n=Ze(t,"Third parameter"),a=function(e,r){I(e,r);var t=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,n=null==e?void 0:e.start,a=null==e?void 0:e.transform,i=null==e?void 0:e.writableType;return{flush:void 0===t?void 0:It(t,e,r+" has member 'flush' that"),readableType:o,start:void 0===n?void 0:Ft(n,e,r+" has member 'start' that"),transform:void 0===a?void 0:Lt(a,e,r+" has member 'transform' that"),writableType:i}}(e,"First parameter");if(void 0!==a.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw new RangeError("Invalid writableType specified");var i,l=Je(n,0),u=Ke(n),s=Je(o,1),b=Ke(o);!function(e,r,t,o,n,a){function i(){return r}function l(r){return function(e,r){var t=e._transformStreamController;if(e._backpressure){return m(e._backpressureChangePromise,(function(){var o=e._writable;if("erroring"===o._state)throw o._storedError;return Xt(t,r)}))}return Xt(t,r)}(e,r)}function u(r){return function(e,r){return Yt(e,r),d(void 0)}(e,r)}function s(){return function(e){var r=e._readable,t=e._transformStreamController,o=t._flushAlgorithm();return Ut(t),m(o,(function(){if("errored"===r._state)throw r._storedError;nt(r._readableStreamController)}),(function(t){throw Yt(e,t),r._storedError}))}(e)}function c(){return function(e){return Nt(e,!1),e._backpressureChangePromise}(e)}function f(r){return xt(e,r),d(void 0)}e._writable=function(e,r,t,o,n,a){void 0===n&&(n=1),void 0===a&&(a=function(){return 1});var i=Object.create(ir.prototype);return ur(i),Er(i,Object.create(qr.prototype),e,r,t,o,n,a),i}(i,l,s,u,t,o),e._readable=vt(i,c,f,n,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,Nt(e,!0),e._transformStreamController=void 0}(this,c((function(e){i=e})),s,b,l,u),function(e,r){var t=Object.create(Ht.prototype),o=function(e){try{return Gt(t,e),d(void 0)}catch(e){return f(e)}},n=function(){return d(void 0)};void 0!==r.transform&&(o=function(e){return r.transform(e,t)});void 0!==r.flush&&(n=function(){return r.flush(t)});!function(e,r,t,o){r._controlledTransformStream=e,e._transformStreamController=r,r._transformAlgorithm=t,r._flushAlgorithm=o}(e,t,o,n)}(this,a),void 0!==a.start?i(a.start(this._transformStreamController)):i(void 0)}return Object.defineProperty(TransformStream.prototype,"readable",{get:function(){if(!Qt(this))throw Kt("readable");return this._readable},enumerable:!1,configurable:!0}),Object.defineProperty(TransformStream.prototype,"writable",{get:function(){if(!Qt(this))throw Kt("writable");return this._writable},enumerable:!1,configurable:!0}),TransformStream}();function Qt(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof Mt)}function Yt(e,r){it(e._readable._readableStreamController,r),xt(e,r)}function xt(e,r){Ut(e._transformStreamController),kr(e._writable._writableStreamController,r),e._backpressure&&Nt(e,!1)}function Nt(e,r){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=c((function(r){e._backpressureChangePromise_resolve=r})),e._backpressure=r}Object.defineProperties(Mt.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(Mt.prototype,r.toStringTag,{value:"TransformStream",configurable:!0});var Ht=function(){function TransformStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(TransformStreamDefaultController.prototype,"desiredSize",{get:function(){if(!Vt(this))throw Jt("desiredSize");return lt(this._controlledTransformStream._readable._readableStreamController)},enumerable:!1,configurable:!0}),TransformStreamDefaultController.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!Vt(this))throw Jt("enqueue");Gt(this,e)},TransformStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!Vt(this))throw Jt("error");var r;r=e,Yt(this._controlledTransformStream,r)},TransformStreamDefaultController.prototype.terminate=function(){if(!Vt(this))throw Jt("terminate");!function(e){var r=e._controlledTransformStream;nt(r._readable._readableStreamController);var t=new TypeError("TransformStream terminated");xt(r,t)}(this)},TransformStreamDefaultController}();function Vt(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof Ht)}function Ut(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function Gt(e,r){var t=e._controlledTransformStream,o=t._readable._readableStreamController;if(!ut(o))throw new TypeError("Readable side is not in a state that permits enqueue");try{at(o,r)}catch(e){throw xt(t,e),t._readable._storedError}(function(e){return!tt(e)})(o)!==t._backpressure&&Nt(t,!0)}function Xt(e,r){return m(e._transformAlgorithm(r),void 0,(function(r){throw Yt(e._controlledTransformStream,r),r}))}function Jt(e){return new TypeError("TransformStreamDefaultController.prototype."+e+" can only be used on a TransformStreamDefaultController")}function Kt(e){return new TypeError("TransformStream.prototype."+e+" can only be used on a TransformStream")}Object.defineProperties(Ht.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(Ht.prototype,r.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});var Zt={ReadableStream:yt,ReadableStreamDefaultController:$r,ReadableByteStreamController:he,ReadableStreamBYOBRequest:_e,ReadableStreamDefaultReader:$,ReadableStreamBYOBReader:Ve,WritableStream:ir,WritableStreamDefaultController:qr,WritableStreamDefaultWriter:vr,ByteLengthQueuingStrategy:Wt,CountQueuingStrategy:At,TransformStream:Mt,TransformStreamDefaultController:Ht};if(void 0!==o)for(var $t in Zt)Object.prototype.hasOwnProperty.call(Zt,$t)&&Object.defineProperty(o,$t,{value:Zt[$t],writable:!0,configurable:!0});e.ByteLengthQueuingStrategy=Wt,e.CountQueuingStrategy=At,e.ReadableByteStreamController=he,e.ReadableStream=yt,e.ReadableStreamBYOBReader=Ve,e.ReadableStreamBYOBRequest=_e,e.ReadableStreamDefaultController=$r,e.ReadableStreamDefaultReader=$,e.TransformStream=Mt,e.TransformStreamDefaultController=Ht,e.WritableStream=ir,e.WritableStreamDefaultController=qr,e.WritableStreamDefaultWriter=vr,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=polyfill.min.js.map

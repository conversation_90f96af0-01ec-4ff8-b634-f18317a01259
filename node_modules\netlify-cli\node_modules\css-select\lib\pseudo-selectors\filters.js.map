{"version": 3, "file": "filters.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/0f0725a9dfeddd2fdb54eda9656cdbab5bbf6be6/src/", "sources": ["pseudo-selectors/filters.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAAkC;AAClC,sDAAgC;AAUhC,SAAS,YAAY,CACjB,IAAgC,EAChC,OAAmC;IAEnC,OAAO,UAAC,IAAI;QACR,IAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACvC,OAAO,MAAM,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;IACjE,CAAC,CAAC;AACN,CAAC;AAEY,QAAA,OAAO,GAA2B;IAC3C,QAAQ,YAAC,IAAI,EAAE,IAAI,EAAE,EAAW;YAAT,OAAO,aAAA;QAC1B,OAAO,SAAS,QAAQ,CAAC,IAAI;YACzB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC,CAAC;IACN,CAAC;IACD,SAAS,YAAC,IAAI,EAAE,IAAI,EAAE,EAAW;YAAT,OAAO,aAAA;QAC3B,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEjC,OAAO,SAAS,SAAS,CAAC,IAAI;YAC1B,OAAO,CACH,IAAI,CAAC,IAAI,CAAC;gBACV,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CACtD,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED,4BAA4B;IAC5B,WAAW,YAAC,IAAI,EAAE,IAAI,EAAE,EAAmB;YAAjB,OAAO,aAAA,EAAE,MAAM,YAAA;QACrC,IAAM,IAAI,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,CAAC;QAE7B,IAAI,IAAI,KAAK,kBAAQ,CAAC,SAAS;YAAE,OAAO,kBAAQ,CAAC,SAAS,CAAC;QAC3D,IAAI,IAAI,KAAK,kBAAQ,CAAC,QAAQ;YAAE,OAAO,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEnE,OAAO,SAAS,QAAQ,CAAC,IAAI;YACzB,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,GAAG,GAAG,CAAC,CAAC;YAEZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAAE,MAAM;gBACrC,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;oBAC5B,GAAG,EAAE,CAAC;iBACT;aACJ;YAED,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC;IACN,CAAC;IACD,gBAAgB,YAAC,IAAI,EAAE,IAAI,EAAE,EAAmB;YAAjB,OAAO,aAAA,EAAE,MAAM,YAAA;QAC1C,IAAM,IAAI,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,CAAC;QAE7B,IAAI,IAAI,KAAK,kBAAQ,CAAC,SAAS;YAAE,OAAO,kBAAQ,CAAC,SAAS,CAAC;QAC3D,IAAI,IAAI,KAAK,kBAAQ,CAAC,QAAQ;YAAE,OAAO,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEnE,OAAO,SAAS,YAAY,CAAC,IAAI;YAC7B,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,GAAG,GAAG,CAAC,CAAC;YAEZ,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC3C,IAAI,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAAE,MAAM;gBACrC,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;oBAC5B,GAAG,EAAE,CAAC;iBACT;aACJ;YAED,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC;IACN,CAAC;IACD,aAAa,YAAC,IAAI,EAAE,IAAI,EAAE,EAAmB;YAAjB,OAAO,aAAA,EAAE,MAAM,YAAA;QACvC,IAAM,IAAI,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,CAAC;QAE7B,IAAI,IAAI,KAAK,kBAAQ,CAAC,SAAS;YAAE,OAAO,kBAAQ,CAAC,SAAS,CAAC;QAC3D,IAAI,IAAI,KAAK,kBAAQ,CAAC,QAAQ;YAAE,OAAO,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEnE,OAAO,SAAS,SAAS,CAAC,IAAI;YAC1B,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,GAAG,GAAG,CAAC,CAAC;YAEZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACnC,IAAI,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC;oBAAE,MAAM;gBACxC,IACI,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC;oBAC7B,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAC3D;oBACE,GAAG,EAAE,CAAC;iBACT;aACJ;YAED,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC;IACN,CAAC;IACD,kBAAkB,YAAC,IAAI,EAAE,IAAI,EAAE,EAAmB;YAAjB,OAAO,aAAA,EAAE,MAAM,YAAA;QAC5C,IAAM,IAAI,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,CAAC;QAE7B,IAAI,IAAI,KAAK,kBAAQ,CAAC,SAAS;YAAE,OAAO,kBAAQ,CAAC,SAAS,CAAC;QAC3D,IAAI,IAAI,KAAK,kBAAQ,CAAC,QAAQ;YAAE,OAAO,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEnE,OAAO,SAAS,aAAa,CAAC,IAAI;YAC9B,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,GAAG,GAAG,CAAC,CAAC;YAEZ,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC3C,IAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACnC,IAAI,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC;oBAAE,MAAM;gBACxC,IACI,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC;oBAC7B,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAC3D;oBACE,GAAG,EAAE,CAAC;iBACT;aACJ;YAED,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC;IACN,CAAC;IAED,yCAAyC;IACzC,IAAI,YAAC,IAAI,EAAE,KAAK,EAAE,EAAW;YAAT,OAAO,aAAA;QACvB,OAAO,UAAC,IAAI;YACR,IAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACvC,OAAO,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QACpE,CAAC,CAAC;IACN,CAAC;IAED,KAAK,EAAL,UACI,IAAgC,EAChC,IAAY,EACZ,OAA2C,EAC3C,OAAgB;QAER,IAAA,MAAM,GAAK,OAAO,OAAZ,CAAa;QAE3B,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,sBAAsB;YACtB,OAAO,eAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;SAC/C;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,8DAA8D;YAC9D,OAAO,UAAC,IAAI,IAAK,OAAA,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAtC,CAAsC,CAAC;SAC3D;QAED,OAAO,UAAC,IAAI,IAAK,OAAA,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAApC,CAAoC,CAAC;IAC1D,CAAC;IAED,KAAK,EAAE,kBAAkB,CAAC,WAAW,CAAC;IACtC,OAAO,EAAE,kBAAkB,CAAC,WAAW,CAAC;IACxC,MAAM,EAAE,kBAAkB,CAAC,UAAU,CAAC;CACzC,CAAC;AAEF;;;;;GAKG;AACH,SAAS,kBAAkB,CACvB,IAA4C;IAE5C,OAAO,SAAS,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,EAAW;YAAT,OAAO,aAAA;QAChD,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAE3B,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;YAC5B,OAAO,kBAAQ,CAAC,SAAS,CAAC;SAC7B;QAED,OAAO,SAAS,MAAM,CAAC,IAAI;YACvB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC;IACN,CAAC,CAAC;AACN,CAAC"}
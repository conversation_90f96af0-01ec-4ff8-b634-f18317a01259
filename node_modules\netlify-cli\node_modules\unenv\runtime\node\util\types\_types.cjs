"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isWeakSet = exports.isWeakMap = exports.isUint8ClampedArray = exports.isUint8Array = exports.isUint32Array = exports.isUint16Array = exports.isTypedArray = exports.isSymbolObject = exports.isStringObject = exports.isSharedArrayBuffer = exports.isSetIterator = exports.isSet = exports.isRegExp = exports.isProxy = exports.isPromise = exports.isNumberObject = exports.isNativeError = exports.isModuleNamespaceObject = exports.isMapIterator = exports.isMap = exports.isKeyObject = exports.isInt8Array = exports.isInt32Array = exports.isInt16Array = exports.isGeneratorObject = exports.isGeneratorFunction = exports.isFloat64Array = exports.isFloat32Array = exports.isExternal = exports.isDate = exports.isDataView = exports.isCryptoKey = exports.isBoxedPrimitive = exports.isBooleanObject = exports.isBigUint64Array = exports.isBigIntObject = exports.isBigInt64Array = exports.isAsyncFunction = exports.isArrayBufferView = exports.isArrayBuffer = exports.isArgumentsObject = exports.isAnyArrayBuffer = void 0;
var _utils = require("../../../_internal/utils.cjs");
const isExternal = exports.isExternal = (0, _utils.notImplemented)("util.types.isExternal");
const isDate = val => val instanceof Date;
exports.isDate = isDate;
const isArgumentsObject = exports.isArgumentsObject = (0, _utils.notImplemented)("util.types.isArgumentsObject");
const isBigIntObject = val => val instanceof BigInt;
exports.isBigIntObject = isBigIntObject;
const isBooleanObject = val => val instanceof Boolean;
exports.isBooleanObject = isBooleanObject;
const isNumberObject = val => val instanceof Number;
exports.isNumberObject = isNumberObject;
const isStringObject = val => val instanceof String;
exports.isStringObject = isStringObject;
const isSymbolObject = val => val instanceof Symbol;
exports.isSymbolObject = isSymbolObject;
const isNativeError = exports.isNativeError = (0, _utils.notImplemented)("util.types.isNativeError");
const isRegExp = val => val instanceof RegExp;
exports.isRegExp = isRegExp;
const isAsyncFunction = exports.isAsyncFunction = (0, _utils.notImplemented)("util.types.isAsyncFunction");
const isGeneratorFunction = exports.isGeneratorFunction = (0, _utils.notImplemented)("util.types.isGeneratorFunction");
const isGeneratorObject = exports.isGeneratorObject = (0, _utils.notImplemented)("util.types.isGeneratorObject");
const isPromise = val => val instanceof Promise;
exports.isPromise = isPromise;
const isMap = val => val instanceof Map;
exports.isMap = isMap;
const isSet = val => val instanceof Set;
exports.isSet = isSet;
const isMapIterator = exports.isMapIterator = (0, _utils.notImplemented)("util.types.isMapIterator");
const isSetIterator = exports.isSetIterator = (0, _utils.notImplemented)("util.types.isSetIterator");
const isWeakMap = val => val instanceof WeakMap;
exports.isWeakMap = isWeakMap;
const isWeakSet = val => val instanceof WeakSet;
exports.isWeakSet = isWeakSet;
const isArrayBuffer = val => val instanceof ArrayBuffer;
exports.isArrayBuffer = isArrayBuffer;
const isDataView = val => val instanceof DataView;
exports.isDataView = isDataView;
const isSharedArrayBuffer = val => val instanceof SharedArrayBuffer;
exports.isSharedArrayBuffer = isSharedArrayBuffer;
const isProxy = exports.isProxy = (0, _utils.notImplemented)("util.types.isProxy");
const isModuleNamespaceObject = exports.isModuleNamespaceObject = (0, _utils.notImplemented)("util.types.isModuleNamespaceObject");
const isAnyArrayBuffer = exports.isAnyArrayBuffer = (0, _utils.notImplemented)("util.types.isAnyArrayBuffer");
const isBoxedPrimitive = exports.isBoxedPrimitive = (0, _utils.notImplemented)("util.types.isBoxedPrimitive");
const isArrayBufferView = exports.isArrayBufferView = (0, _utils.notImplemented)("util.types.isArrayBufferView");
const isTypedArray = exports.isTypedArray = (0, _utils.notImplemented)("util.types.isTypedArray");
const isUint8Array = exports.isUint8Array = (0, _utils.notImplemented)("util.types.isUint8Array");
const isUint8ClampedArray = exports.isUint8ClampedArray = (0, _utils.notImplemented)("util.types.isUint8ClampedArray");
const isUint16Array = exports.isUint16Array = (0, _utils.notImplemented)("util.types.isUint16Array");
const isUint32Array = exports.isUint32Array = (0, _utils.notImplemented)("util.types.isUint32Array");
const isInt8Array = exports.isInt8Array = (0, _utils.notImplemented)("util.types.isInt8Array");
const isInt16Array = exports.isInt16Array = (0, _utils.notImplemented)("util.types.isInt16Array");
const isInt32Array = exports.isInt32Array = (0, _utils.notImplemented)("util.types.isInt32Array");
const isFloat32Array = exports.isFloat32Array = (0, _utils.notImplemented)("util.types.isFloat32Array");
const isFloat64Array = exports.isFloat64Array = (0, _utils.notImplemented)("util.types.isFloat64Array");
const isBigInt64Array = exports.isBigInt64Array = (0, _utils.notImplemented)("util.types.isBigInt64Array");
const isBigUint64Array = exports.isBigUint64Array = (0, _utils.notImplemented)("util.types.isBigUint64Array");
const isKeyObject = exports.isKeyObject = (0, _utils.notImplemented)("util.types.isKeyObject");
const isCryptoKey = exports.isCryptoKey = (0, _utils.notImplemented)("util.types.isCryptoKey");
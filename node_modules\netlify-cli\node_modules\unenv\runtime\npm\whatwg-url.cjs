"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.setTheUsername = exports.setThePassword = exports.serializeURLOrigin = exports.serializeURL = exports.serializeInteger = exports.serializeHost = exports.percentDecodeString = exports.percentDecodeBytes = exports.parseURL = exports.cannotHaveAUsernamePasswordPort = exports.basicURLParse = exports.URLSearchParams = exports.URL = void 0;
var _utils = require("../_internal/utils.cjs");
const URL = exports.URL = globalThis.URL;
const URLSearchParams = exports.URLSearchParams = globalThis.URLSearchParams;
const parseURL = exports.parseURL = (0, _utils.notImplemented)("whatwg-url.parseURL");
const basicURLParse = exports.basicURLParse = (0, _utils.notImplemented)("whatwg-url.basicURLParse");
const serializeURL = exports.serializeURL = (0, _utils.notImplemented)("whatwg-url.serializeURL");
const serializeHost = exports.serializeHost = (0, _utils.notImplemented)("whatwg-url.serializeHost");
const serializeInteger = exports.serializeInteger = (0, _utils.notImplemented)("whatwg-url.serializeInteger");
const serializeURLOrigin = exports.serializeURLOrigin = (0, _utils.notImplemented)("whatwg-url.serializeURLOrigin");
const setTheUsername = exports.setTheUsername = (0, _utils.notImplemented)("whatwg-url.setTheUsername");
const setThePassword = exports.setThePassword = (0, _utils.notImplemented)("whatwg-url.setThePassword");
const cannotHaveAUsernamePasswordPort = exports.cannotHaveAUsernamePasswordPort = (0, _utils.notImplemented)("whatwg-url.cannotHaveAUsernamePasswordPort");
const percentDecodeBytes = exports.percentDecodeBytes = (0, _utils.notImplemented)("whatwg-url.percentDecodeBytes");
const percentDecodeString = exports.percentDecodeString = (0, _utils.notImplemented)("whatwg-url.percentDecodeString");